package ltd.zstech.standard.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.standard.entity.TocTreeNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强的目录映射器
 * 负责将目录树与拆分后的文档进行智能映射
 */
@Slf4j
public class EnhancedTocMapper {
    
    // 映射阈值
    private static final double EXACT_MATCH_THRESHOLD = 1.0;
    private static final double HIGH_MATCH_THRESHOLD = 0.8;
    private static final double MEDIUM_MATCH_THRESHOLD = 0.6;
    private static final double LOW_MATCH_THRESHOLD = 0.4;
    
    /**
     * 执行增强的目录映射
     * @param tocTree 目录树
     * @param splitDocuments 拆分后的文档列表
     * @return 映射结果
     */
    public static JSONObject performEnhancedMapping(List<TocTreeNode> tocTree, 
                                                   List<JSONObject> splitDocuments) {
        log.info("开始执行增强目录映射，目录树节点: {}, 拆分文档: {}", 
                TocTreeBuilder.flattenTree(tocTree).size(), splitDocuments.size());
        
        // 1. 预处理：识别特殊文档（首页、目次）
        Map<String, JSONObject> specialDocuments = identifySpecialDocuments(splitDocuments);
        
        // 2. 获取所有叶子节点进行映射
        List<TocTreeNode> allNodes = TocTreeBuilder.flattenTree(tocTree);
        
        // 3. 创建文档映射状态跟踪
        Map<String, Boolean> documentMapped = new HashMap<>();
        for (JSONObject doc : splitDocuments) {
            documentMapped.put(doc.getString("fileName"), false);
        }
        
        // 4. 执行映射
        List<JSONObject> mappingResults = new ArrayList<>();
        int successfulMappings = 0;
        
        for (TocTreeNode node : allNodes) {
            JSONObject mappingResult = mapNodeToDocument(node, splitDocuments, documentMapped);
            mappingResults.add(mappingResult);
            
            if ("MAPPED".equals(mappingResult.getString("status"))) {
                successfulMappings++;
                String mappedFileName = mappingResult.getJSONObject("document").getString("fileName");
                documentMapped.put(mappedFileName, true);
            }
        }
        
        // 5. 处理未映射的文档
        List<JSONObject> unmappedDocuments = new ArrayList<>();
        for (JSONObject doc : splitDocuments) {
            String fileName = doc.getString("fileName");
            if (!documentMapped.get(fileName)) {
                unmappedDocuments.add(doc);
            }
        }
        
        // 6. 构建结果
        JSONObject result = new JSONObject();
        result.put("tocTree", convertTreeToJSON(tocTree));
        result.put("mappingResults", mappingResults);
        result.put("unmappedDocuments", unmappedDocuments);
        result.put("specialDocuments", specialDocuments);
        result.put("statistics", buildMappingStatistics(allNodes, splitDocuments, successfulMappings));
        
        log.info("增强目录映射完成，成功映射: {}/{}, 未映射文档: {}", 
                successfulMappings, allNodes.size(), unmappedDocuments.size());
        
        return result;
    }
    
    /**
     * 识别特殊文档（首页、目次等）
     */
    private static Map<String, JSONObject> identifySpecialDocuments(List<JSONObject> splitDocuments) {
        Map<String, JSONObject> specialDocs = new HashMap<>();
        
        for (JSONObject doc : splitDocuments) {
            String fileName = doc.getString("fileName");
            if (fileName == null) continue;
            
            String cleanFileName = fileName.toLowerCase().trim();
            
            if (cleanFileName.contains("首页") || cleanFileName.contains("封面")) {
                specialDocs.put("FRONT_PAGE", doc);
                log.info("识别到首页文档: {}", fileName);
            } else if (cleanFileName.contains("目次") || cleanFileName.contains("目录")) {
                specialDocs.put("TOC_PAGE", doc);
                log.info("识别到目次文档: {}", fileName);
            }
        }
        
        return specialDocs;
    }
    
    /**
     * 将单个节点映射到文档
     */
    private static JSONObject mapNodeToDocument(TocTreeNode node, 
                                               List<JSONObject> splitDocuments,
                                               Map<String, Boolean> documentMapped) {
        JSONObject result = new JSONObject();
        result.put("node", node.toJSONObject());
        result.put("document", null);
        result.put("score", 0.0);
        result.put("status", "UNMAPPED");
        result.put("matchType", "NONE");
        
        // 如果节点已经有映射的文档，直接返回
        if (node.getMappedDocument() != null) {
            result.put("document", node.getMappedDocument());
            result.put("score", node.getMappingScore());
            result.put("status", node.getMappingStatus());
            result.put("matchType", "PRE_MAPPED");
            return result;
        }
        
        JSONObject bestMatch = null;
        double bestScore = 0.0;
        String bestMatchType = "NONE";
        
        // 遍历所有未映射的文档
        for (JSONObject doc : splitDocuments) {
            String fileName = doc.getString("fileName");
            if (documentMapped.get(fileName)) {
                continue; // 跳过已映射的文档
            }
            
            // 计算匹配得分
            double score = calculateMatchScore(node, doc);
            String matchType = determineMatchType(score);
            
            if (score > bestScore && score >= LOW_MATCH_THRESHOLD) {
                bestScore = score;
                bestMatch = doc;
                bestMatchType = matchType;
            }
        }
        
        // 设置映射结果
        if (bestMatch != null) {
            result.put("document", bestMatch);
            result.put("score", bestScore);
            result.put("status", bestScore >= MEDIUM_MATCH_THRESHOLD ? "MAPPED" : "PARTIAL");
            result.put("matchType", bestMatchType);
            
            // 更新节点的映射信息
            node.setMappingInfo(bestMatch, bestScore);
            
            log.debug("节点映射: {} -> {} (得分: {}, 类型: {})", 
                    node.getTitle(), bestMatch.getString("fileName"), bestScore, bestMatchType);
        }
        
        return result;
    }
    
    /**
     * 计算节点与文档的匹配得分
     */
    private static double calculateMatchScore(TocTreeNode node, JSONObject document) {
        String nodeTitle = node.getTitle();
        String fileName = document.getString("fileName");
        
        if (nodeTitle == null || fileName == null) {
            return 0.0;
        }
        
        // 从文件名中提取标题
        String docTitle = extractTitleFromFileName(fileName);
        
        // 标准化标题
        String normalizedNodeTitle = normalizeTitle(nodeTitle);
        String normalizedDocTitle = normalizeTitle(docTitle);
        
        double score = 0.0;
        
        // 1. 完全匹配
        if (normalizedNodeTitle.equals(normalizedDocTitle)) {
            score = 1.0;
        }
        // 2. 包含关系匹配
        else if (normalizedNodeTitle.contains(normalizedDocTitle) || 
                 normalizedDocTitle.contains(normalizedNodeTitle)) {
            double containmentRatio = Math.min(normalizedNodeTitle.length(), normalizedDocTitle.length()) /
                                    (double) Math.max(normalizedNodeTitle.length(), normalizedDocTitle.length());
            score = 0.8 * containmentRatio;
        }
        // 3. 编辑距离匹配
        else {
            int editDistance = calculateEditDistance(normalizedNodeTitle, normalizedDocTitle);
            int maxLength = Math.max(normalizedNodeTitle.length(), normalizedDocTitle.length());
            if (maxLength > 0) {
                score = Math.max(0.0, 1.0 - (double) editDistance / maxLength);
            }
        }
        
        // 4. 特殊类型加分
        score += calculateTypeBonus(node, document);
        
        // 5. 关键词匹配加分
        score += calculateKeywordBonus(nodeTitle, docTitle);
        
        return Math.min(1.0, score);
    }
    
    /**
     * 计算类型匹配加分
     */
    private static double calculateTypeBonus(TocTreeNode node, JSONObject document) {
        String nodeType = node.getNodeType();
        String fileName = document.getString("fileName");
        
        if (fileName == null) return 0.0;
        
        String lowerFileName = fileName.toLowerCase();
        
        // 附录类型匹配
        if ("APPENDIX".equals(nodeType) && 
            (lowerFileName.contains("附录") || lowerFileName.contains("appendix"))) {
            return 0.2;
        }
        
        // 前言类型匹配
        if (node.getTitle() != null && node.getTitle().contains("前言") && 
            lowerFileName.contains("前言")) {
            return 0.2;
        }
        
        return 0.0;
    }
    
    /**
     * 计算关键词匹配加分
     */
    private static double calculateKeywordBonus(String title1, String title2) {
        if (title1 == null || title2 == null) return 0.0;
        
        String[] keywords = {"管理", "制度", "规定", "办法", "流程", "标准", "规范", "要求", "职责", "范围"};
        int matchCount = 0;
        
        for (String keyword : keywords) {
            if (title1.contains(keyword) && title2.contains(keyword)) {
                matchCount++;
            }
        }
        
        return matchCount > 0 ? 0.1 * matchCount / keywords.length : 0.0;
    }
    
    /**
     * 从文件名中提取标题
     */
    private static String extractTitleFromFileName(String fileName) {
        if (fileName == null) return "";
        
        // 去除文件扩展名
        String title = fileName;
        if (title.contains(".")) {
            title = title.substring(0, title.lastIndexOf("."));
        }
        
        // 去除末尾的序号
        if (title.matches(".*\\s*-\\s*\\d+$")) {
            title = title.replaceAll("\\s*-\\s*\\d+$", "");
        }
        
        return title.trim();
    }
    
    /**
     * 标准化标题
     */
    private static String normalizeTitle(String title) {
        if (title == null) return "";
        
        return title.trim()
                .replaceAll("\\s+", "")
                .replaceAll("[\\p{Punct}]", "")
                .toLowerCase();
    }
    
    /**
     * 计算编辑距离
     */
    private static int calculateEditDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) dp[i][0] = i;
        for (int j = 0; j <= n; j++) dp[0][j] = j;
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 确定匹配类型
     */
    private static String determineMatchType(double score) {
        if (score >= EXACT_MATCH_THRESHOLD) return "EXACT";
        if (score >= HIGH_MATCH_THRESHOLD) return "HIGH";
        if (score >= MEDIUM_MATCH_THRESHOLD) return "MEDIUM";
        if (score >= LOW_MATCH_THRESHOLD) return "LOW";
        return "NONE";
    }
    
    /**
     * 将目录树转换为JSON格式
     */
    private static List<JSONObject> convertTreeToJSON(List<TocTreeNode> treeNodes) {
        List<JSONObject> jsonTree = new ArrayList<>();
        for (TocTreeNode node : treeNodes) {
            jsonTree.add(node.toJSONObject());
        }
        return jsonTree;
    }
    
    /**
     * 构建映射统计信息
     */
    private static JSONObject buildMappingStatistics(List<TocTreeNode> allNodes, 
                                                    List<JSONObject> splitDocuments,
                                                    int successfulMappings) {
        JSONObject stats = new JSONObject();
        
        stats.put("totalNodes", allNodes.size());
        stats.put("totalDocuments", splitDocuments.size());
        stats.put("successfulMappings", successfulMappings);
        stats.put("unmappedNodes", allNodes.size() - successfulMappings);
        stats.put("mappingRate", allNodes.size() > 0 ? (double) successfulMappings / allNodes.size() : 0.0);
        
        // 按匹配类型统计
        Map<String, Integer> matchTypeCount = new HashMap<>();
        for (TocTreeNode node : allNodes) {
            String status = node.getMappingStatus();
            matchTypeCount.put(status, matchTypeCount.getOrDefault(status, 0) + 1);
        }
        stats.put("matchTypeDistribution", matchTypeCount);
        
        return stats;
    }
}
