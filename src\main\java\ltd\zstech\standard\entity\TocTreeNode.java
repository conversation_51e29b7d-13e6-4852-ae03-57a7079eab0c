package ltd.zstech.standard.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 目录树节点实体类
 * 用于表示文档目录的层级结构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TocTreeNode {
    
    /**
     * 目录项标题
     */
    private String title;
    
    /**
     * 目录项级别（1-6）
     */
    private Integer level;
    
    /**
     * 页码
     */
    private String pageNumber;
    
    /**
     * 在原始目录中的索引
     */
    private Integer index;
    
    /**
     * 原始文本
     */
    private String originalText;
    
    /**
     * 子节点列表
     */
    private List<TocTreeNode> children;
    
    /**
     * 对应的拆分文档信息
     */
    private JSONObject mappedDocument;
    
    /**
     * 映射状态
     */
    private String mappingStatus; // "MAPPED", "UNMAPPED", "PARTIAL"
    
    /**
     * 映射得分
     */
    private Double mappingScore;
    
    /**
     * 节点类型
     */
    private String nodeType; // "FRONT_PAGE", "TOC_PAGE", "CONTENT", "APPENDIX"
    
    /**
     * 是否为叶子节点
     */
    private Boolean isLeaf;
    
    /**
     * 父节点引用（用于向上遍历）
     */
    private TocTreeNode parent;
    
    /**
     * 构造函数 - 从JSONObject创建
     */
    public TocTreeNode(JSONObject tocItem) {
        this.title = tocItem.getString("title");
        this.level = tocItem.getInteger("level");
        this.pageNumber = tocItem.getString("pageNumber");
        this.index = tocItem.getInteger("index");
        this.originalText = tocItem.getString("originalText");
        this.children = new ArrayList<>();
        this.mappingStatus = "UNMAPPED";
        this.mappingScore = 0.0;
        this.isLeaf = true;
        this.nodeType = determineNodeType(this.title);
    }
    
    /**
     * 添加子节点
     */
    public void addChild(TocTreeNode child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        child.setParent(this);
        this.children.add(child);
        this.isLeaf = false;
    }
    
    /**
     * 获取所有叶子节点
     */
    public List<TocTreeNode> getLeafNodes() {
        List<TocTreeNode> leafNodes = new ArrayList<>();
        collectLeafNodes(this, leafNodes);
        return leafNodes;
    }
    
    /**
     * 递归收集叶子节点
     */
    private void collectLeafNodes(TocTreeNode node, List<TocTreeNode> leafNodes) {
        if (node.getIsLeaf()) {
            leafNodes.add(node);
        } else {
            for (TocTreeNode child : node.getChildren()) {
                collectLeafNodes(child, leafNodes);
            }
        }
    }
    
    /**
     * 获取节点路径（从根到当前节点）
     */
    public List<String> getNodePath() {
        List<String> path = new ArrayList<>();
        TocTreeNode current = this;
        while (current != null) {
            path.add(0, current.getTitle());
            current = current.getParent();
        }
        return path;
    }
    
    /**
     * 转换为JSONObject
     */
    public JSONObject toJSONObject() {
        JSONObject json = new JSONObject();
        json.put("title", this.title);
        json.put("level", this.level);
        json.put("pageNumber", this.pageNumber);
        json.put("index", this.index);
        json.put("originalText", this.originalText);
        json.put("mappingStatus", this.mappingStatus);
        json.put("mappingScore", this.mappingScore);
        json.put("nodeType", this.nodeType);
        json.put("isLeaf", this.isLeaf);
        json.put("mappedDocument", this.mappedDocument);
        
        if (this.children != null && !this.children.isEmpty()) {
            List<JSONObject> childrenJson = new ArrayList<>();
            for (TocTreeNode child : this.children) {
                childrenJson.add(child.toJSONObject());
            }
            json.put("children", childrenJson);
        }
        
        return json;
    }
    
    /**
     * 根据标题确定节点类型
     */
    private String determineNodeType(String title) {
        if (title == null) return "CONTENT";
        
        String cleanTitle = title.trim().toLowerCase();
        
        if (cleanTitle.contains("首页") || cleanTitle.contains("封面")) {
            return "FRONT_PAGE";
        } else if (cleanTitle.contains("目次") || cleanTitle.contains("目录")) {
            return "TOC_PAGE";
        } else if (cleanTitle.contains("附录") || cleanTitle.startsWith("appendix") || 
                   cleanTitle.matches("^[a-z]\\s+.*")) {
            return "APPENDIX";
        } else {
            return "CONTENT";
        }
    }
    
    /**
     * 检查是否可以映射到指定的文档标题
     */
    public boolean canMapTo(String documentTitle) {
        if (documentTitle == null || this.title == null) {
            return false;
        }
        
        // 标准化标题进行比较
        String normalizedTocTitle = normalizeTitle(this.title);
        String normalizedDocTitle = normalizeTitle(documentTitle);
        
        // 完全匹配
        if (normalizedTocTitle.equals(normalizedDocTitle)) {
            return true;
        }
        
        // 包含关系匹配
        if (normalizedTocTitle.contains(normalizedDocTitle) || 
            normalizedDocTitle.contains(normalizedTocTitle)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 标准化标题
     */
    private String normalizeTitle(String title) {
        return title.trim()
                .replaceAll("\\s+", "")
                .replaceAll("[\\p{Punct}]", "")
                .toLowerCase();
    }
    
    /**
     * 设置映射信息
     */
    public void setMappingInfo(JSONObject document, double score) {
        this.mappedDocument = document;
        this.mappingScore = score;
        this.mappingStatus = score > 0.6 ? "MAPPED" : "PARTIAL";
    }
    
    /**
     * 清除映射信息
     */
    public void clearMapping() {
        this.mappedDocument = null;
        this.mappingScore = 0.0;
        this.mappingStatus = "UNMAPPED";
    }
    
    /**
     * 获取深度（从根节点开始计算）
     */
    public int getDepth() {
        int depth = 0;
        TocTreeNode current = this.parent;
        while (current != null) {
            depth++;
            current = current.getParent();
        }
        return depth;
    }
    
    /**
     * 获取子节点数量
     */
    public int getChildCount() {
        return this.children != null ? this.children.size() : 0;
    }
    
    /**
     * 获取所有后代节点数量
     */
    public int getDescendantCount() {
        int count = 0;
        if (this.children != null) {
            for (TocTreeNode child : this.children) {
                count += 1 + child.getDescendantCount();
            }
        }
        return count;
    }
}
