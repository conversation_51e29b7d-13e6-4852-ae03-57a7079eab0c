package ltd.zstech.standard.entity;

import com.aspose.words.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSection {
    public String Title;
    public Node StartNode;
    public List<Node> Content;

    // 新增字段用于标识段落类型
    public String SectionType; // "FRONT_PAGE", "TOC_PAGE", "CONTENT"
    public Integer OriginalIndex; // 在原始文档中的索引

    // 兼容性构造函数
    public DocumentSection(String title, Node startNode, List<Node> content) {
        this.Title = title;
        this.StartNode = startNode;
        this.Content = content;
        this.SectionType = "CONTENT";
        this.OriginalIndex = 0;
    }
}
