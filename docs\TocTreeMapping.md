# 目录树形结构与增强映射功能

## 概述

本功能实现了Word文档目录项的树形结构构建和与拆分文档的智能映射，解决了以下问题：

1. **目录项层级结构** - 将扁平的目录项构建成树形结构
2. **拆分数据映射** - 处理拆分后数据比目录项多2个元素（首页和目次）的问题
3. **智能标题映射** - 将目录项中的标题映射到拆分后数据中
4. **树形结构返回** - 根据目录项树形结构构建返回数据

## 核心组件

### 1. 实体类

#### TocTreeNode
目录树节点实体类，表示文档目录的层级结构：

```java
public class TocTreeNode {
    private String title;           // 目录项标题
    private Integer level;          // 目录项级别（1-6）
    private String pageNumber;      // 页码
    private Integer index;          // 在原始目录中的索引
    private List<TocTreeNode> children; // 子节点列表
    private JSONObject mappedDocument;  // 对应的拆分文档信息
    private String mappingStatus;   // 映射状态：MAPPED, UNMAPPED, PARTIAL
    private Double mappingScore;    // 映射得分
    private String nodeType;        // 节点类型：FRONT_PAGE, TOC_PAGE, CONTENT, APPENDIX
    // ... 其他字段和方法
}
```

#### DocumentSection（增强）
文档段落实体类，新增了段落类型标识：

```java
public class DocumentSection {
    public String Title;
    public Node StartNode;
    public List<Node> Content;
    public String SectionType;      // 新增：段落类型
    public Integer OriginalIndex;   // 新增：原始索引
}
```

### 2. 工具类

#### TocTreeBuilder
目录树构建器，负责将扁平的目录项列表构建成层级树形结构：

```java
// 构建基本目录树
List<TocTreeNode> tocTree = TocTreeBuilder.buildTocTree(flatTocList);

// 构建增强目录树（包含首页和目次页）
List<TocTreeNode> enhancedTree = TocTreeBuilder.buildEnhancedTocTree(flatTocList, splitDocuments);

// 获取树统计信息
JSONObject stats = TocTreeBuilder.getTreeStatistics(tocTree);
```

#### EnhancedTocMapper
增强的目录映射器，负责将目录树与拆分后的文档进行智能映射：

```java
// 执行增强映射
JSONObject mappingResult = EnhancedTocMapper.performEnhancedMapping(tocTree, splitDocuments);
```

### 3. 服务方法

#### FileOperationService

##### buildTocTreeAndEnhancedMapping
构建目录树形结构并进行增强映射：

```java
public JSONObject buildTocTreeAndEnhancedMapping(MultipartFile file, Integer templateId, Integer formdataId)
```

返回结果包含：
- `tocTree`: 目录树形结构
- `splitDocuments`: 拆分后的文档列表
- `enhancedMappingResults`: 增强映射结果
- `unmappedDocuments`: 未映射的文档
- `specialDocuments`: 特殊文档（首页、目次）
- `treeStatistics`: 树统计信息
- `mappingStatistics`: 映射统计信息

##### getDocumentTocTree
获取文档的目录树形结构（不进行文档拆分）：

```java
public JSONObject getDocumentTocTree(MultipartFile file)
```

## API接口

### 1. 增强映射接口

#### POST /fileOperation/splitWithEnhancedMapping
文档结构化并进行增强目录树映射

**请求参数：**
```json
{
    "templateId": 1,
    "formdataId": 1,
    "subTemplateId": 2
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "result": {
        "message": "文档结构化和增强目录树映射完成",
        "enhancedMappingDetails": {
            "tocTree": [...],
            "splitDocuments": [...],
            "enhancedMappingResults": [...],
            "treeStatistics": {
                "totalNodes": 10,
                "leafNodes": 6,
                "mappedNodes": 8,
                "mappingRate": 0.8
            }
        }
    }
}
```

### 2. 目录树获取接口

#### POST /fileOperation/getTocTree
获取文档目录树形结构

**请求参数：**
```json
{
    "templateId": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "result": {
        "tocTree": [
            {
                "title": "前言",
                "level": 1,
                "pageNumber": "II",
                "children": [],
                "nodeType": "CONTENT",
                "mappingStatus": "UNMAPPED"
            },
            {
                "title": "1 范围",
                "level": 1,
                "pageNumber": "1",
                "children": [
                    {
                        "title": "1.1 适用范围",
                        "level": 2,
                        "pageNumber": "1",
                        "children": []
                    }
                ]
            }
        ],
        "treeStatistics": {
            "totalNodes": 10,
            "leafNodes": 6,
            "maxDepth": 3,
            "rootNodes": 4
        }
    }
}
```

### 3. 预览接口

#### POST /fileOperation/previewEnhancedMapping
预览文档拆分和增强目录树映射结果

## 映射算法

### 1. 匹配得分计算

映射器使用多种策略计算匹配得分：

1. **完全匹配** (1.0分): 标题完全相同
2. **包含关系匹配** (0.8分): 一个标题包含另一个
3. **编辑距离匹配** (0.0-1.0分): 基于字符串编辑距离
4. **类型匹配加分** (0.2分): 附录、前言等特殊类型匹配
5. **关键词匹配加分** (0.1分): 包含相同的关键词

### 2. 匹配类型

- **EXACT** (1.0分): 精确匹配
- **HIGH** (0.8-0.99分): 高度匹配
- **MEDIUM** (0.6-0.79分): 中等匹配
- **LOW** (0.4-0.59分): 低度匹配
- **NONE** (<0.4分): 无匹配

### 3. 特殊文档处理

系统自动识别和处理特殊文档：

- **首页文档**: 文件名包含"首页"或"封面"
- **目次文档**: 文件名包含"目次"或"目录"
- **附录文档**: 使用AppendixTextProcessor进行专门处理

## 使用示例

### 1. 基本使用

```java
// 构建目录树并进行映射
JSONObject result = fileOperationService.buildTocTreeAndEnhancedMapping(file, templateId, formdataId);

// 获取映射结果
List<JSONObject> mappingResults = result.getJSONArray("enhancedMappingResults").toJavaList(JSONObject.class);

// 处理映射结果
for (JSONObject mapping : mappingResults) {
    if ("MAPPED".equals(mapping.getString("status"))) {
        JSONObject document = mapping.getJSONObject("document");
        JSONObject node = mapping.getJSONObject("node");
        // 处理成功映射的项目
    }
}
```

### 2. 获取树统计信息

```java
JSONObject treeStats = result.getJSONObject("treeStatistics");
int totalNodes = treeStats.getInteger("totalNodes");
int mappedNodes = treeStats.getInteger("mappedNodes");
double mappingRate = treeStats.getDoubleValue("mappingRate");
```

## 测试

项目包含完整的单元测试：

- `TocTreeBuilderTest`: 测试目录树构建功能
- `EnhancedTocMapperTest`: 测试增强映射功能

运行测试：
```bash
mvn test -Dtest=TocTreeBuilderTest
mvn test -Dtest=EnhancedTocMapperTest
```

## 注意事项

1. **性能考虑**: 大型文档可能包含大量目录项，建议对超大文档进行分批处理
2. **内存管理**: 树形结构会占用额外内存，处理完成后及时释放
3. **映射准确性**: 映射算法基于文本相似度，对于格式差异较大的标题可能需要人工调整
4. **兼容性**: 新功能保持与现有API的向后兼容性

## 扩展功能

1. **自定义映射规则**: 可以扩展映射算法，添加特定业务的匹配规则
2. **映射结果缓存**: 对于相同文档的重复处理，可以缓存映射结果
3. **批量处理**: 支持多个文档的批量目录树构建和映射
4. **可视化展示**: 可以基于树形结构数据开发前端可视化组件
