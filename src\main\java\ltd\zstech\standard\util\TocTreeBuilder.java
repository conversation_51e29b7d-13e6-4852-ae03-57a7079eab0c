package ltd.zstech.standard.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.standard.entity.TocTreeNode;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

/**
 * 目录树构建器
 * 负责将扁平的目录项列表构建成层级树形结构
 */
@Slf4j
public class TocTreeBuilder {
    
    /**
     * 构建目录树
     * @param flatTocList 扁平的目录项列表
     * @return 目录树根节点列表
     */
    public static List<TocTreeNode> buildTocTree(List<JSONObject> flatTocList) {
        if (flatTocList == null || flatTocList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<TocTreeNode> rootNodes = new ArrayList<>();
        Stack<TocTreeNode> nodeStack = new Stack<>();
        
        log.info("开始构建目录树，共 {} 个目录项", flatTocList.size());
        
        for (JSONObject tocItem : flatTocList) {
            TocTreeNode currentNode = new TocTreeNode(tocItem);
            int currentLevel = currentNode.getLevel();
            
            // 调整栈，确保当前节点的父级在栈顶
            while (!nodeStack.isEmpty() && nodeStack.peek().getLevel() >= currentLevel) {
                nodeStack.pop();
            }
            
            if (nodeStack.isEmpty()) {
                // 顶级节点
                rootNodes.add(currentNode);
                log.debug("添加根节点: {} (级别: {})", currentNode.getTitle(), currentLevel);
            } else {
                // 添加到父级的children中
                TocTreeNode parent = nodeStack.peek();
                parent.addChild(currentNode);
                log.debug("添加子节点: {} -> {} (级别: {})", 
                    parent.getTitle(), currentNode.getTitle(), currentLevel);
            }
            
            nodeStack.push(currentNode);
        }
        
        log.info("目录树构建完成，共 {} 个根节点", rootNodes.size());
        return rootNodes;
    }
    
    /**
     * 构建增强的目录树（包含首页和目次页）
     * @param flatTocList 扁平的目录项列表
     * @param splitDocuments 拆分后的文档列表
     * @return 增强的目录树
     */
    public static List<TocTreeNode> buildEnhancedTocTree(List<JSONObject> flatTocList, 
                                                        List<JSONObject> splitDocuments) {
        List<TocTreeNode> enhancedTree = new ArrayList<>();
        
        // 1. 创建首页节点（如果存在）
        TocTreeNode frontPageNode = createFrontPageNode(splitDocuments);
        if (frontPageNode != null) {
            enhancedTree.add(frontPageNode);
        }
        
        // 2. 创建目次页节点（如果存在）
        TocTreeNode tocPageNode = createTocPageNode(splitDocuments);
        if (tocPageNode != null) {
            enhancedTree.add(tocPageNode);
        }
        
        // 3. 构建正常的目录树
        List<TocTreeNode> contentTree = buildTocTree(flatTocList);
        enhancedTree.addAll(contentTree);
        
        log.info("增强目录树构建完成，总节点数: {}", enhancedTree.size());
        return enhancedTree;
    }
    
    /**
     * 创建首页节点
     */
    private static TocTreeNode createFrontPageNode(List<JSONObject> splitDocuments) {
        for (JSONObject doc : splitDocuments) {
            String fileName = doc.getString("fileName");
            if (fileName != null && fileName.contains("首页")) {
                TocTreeNode frontPageNode = new TocTreeNode();
                frontPageNode.setTitle("首页");
                frontPageNode.setLevel(0);
                frontPageNode.setPageNumber("I");
                frontPageNode.setIndex(-2);
                frontPageNode.setNodeType("FRONT_PAGE");
                frontPageNode.setMappedDocument(doc);
                frontPageNode.setMappingStatus("MAPPED");
                frontPageNode.setMappingScore(1.0);
                frontPageNode.setIsLeaf(true);
                frontPageNode.setChildren(new ArrayList<>());
                
                log.info("创建首页节点: {}", fileName);
                return frontPageNode;
            }
        }
        return null;
    }
    
    /**
     * 创建目次页节点
     */
    private static TocTreeNode createTocPageNode(List<JSONObject> splitDocuments) {
        for (JSONObject doc : splitDocuments) {
            String fileName = doc.getString("fileName");
            if (fileName != null && (fileName.contains("目次") || fileName.contains("目录"))) {
                TocTreeNode tocPageNode = new TocTreeNode();
                tocPageNode.setTitle("目次");
                tocPageNode.setLevel(0);
                tocPageNode.setPageNumber("II");
                tocPageNode.setIndex(-1);
                tocPageNode.setNodeType("TOC_PAGE");
                tocPageNode.setMappedDocument(doc);
                tocPageNode.setMappingStatus("MAPPED");
                tocPageNode.setMappingScore(1.0);
                tocPageNode.setIsLeaf(true);
                tocPageNode.setChildren(new ArrayList<>());
                
                log.info("创建目次页节点: {}", fileName);
                return tocPageNode;
            }
        }
        return null;
    }
    
    /**
     * 将目录树转换为扁平列表（深度优先遍历）
     * @param treeNodes 目录树根节点列表
     * @return 扁平的节点列表
     */
    public static List<TocTreeNode> flattenTree(List<TocTreeNode> treeNodes) {
        List<TocTreeNode> flatList = new ArrayList<>();
        for (TocTreeNode node : treeNodes) {
            flattenTreeRecursive(node, flatList);
        }
        return flatList;
    }
    
    /**
     * 递归扁平化树结构
     */
    private static void flattenTreeRecursive(TocTreeNode node, List<TocTreeNode> flatList) {
        flatList.add(node);
        if (node.getChildren() != null) {
            for (TocTreeNode child : node.getChildren()) {
                flattenTreeRecursive(child, flatList);
            }
        }
    }
    
    /**
     * 获取树的统计信息
     * @param treeNodes 目录树根节点列表
     * @return 统计信息
     */
    public static JSONObject getTreeStatistics(List<TocTreeNode> treeNodes) {
        JSONObject stats = new JSONObject();
        
        int totalNodes = 0;
        int leafNodes = 0;
        int mappedNodes = 0;
        int maxDepth = 0;
        
        for (TocTreeNode rootNode : treeNodes) {
            TreeStats nodeStats = calculateNodeStats(rootNode, 0);
            totalNodes += nodeStats.totalNodes;
            leafNodes += nodeStats.leafNodes;
            mappedNodes += nodeStats.mappedNodes;
            maxDepth = Math.max(maxDepth, nodeStats.maxDepth);
        }
        
        stats.put("totalNodes", totalNodes);
        stats.put("leafNodes", leafNodes);
        stats.put("mappedNodes", mappedNodes);
        stats.put("unmappedNodes", totalNodes - mappedNodes);
        stats.put("maxDepth", maxDepth);
        stats.put("rootNodes", treeNodes.size());
        stats.put("mappingRate", totalNodes > 0 ? (double) mappedNodes / totalNodes : 0.0);
        
        return stats;
    }
    
    /**
     * 递归计算节点统计信息
     */
    private static TreeStats calculateNodeStats(TocTreeNode node, int currentDepth) {
        TreeStats stats = new TreeStats();
        stats.totalNodes = 1;
        stats.maxDepth = currentDepth;
        
        if (node.getIsLeaf()) {
            stats.leafNodes = 1;
        }
        
        if ("MAPPED".equals(node.getMappingStatus())) {
            stats.mappedNodes = 1;
        }
        
        if (node.getChildren() != null) {
            for (TocTreeNode child : node.getChildren()) {
                TreeStats childStats = calculateNodeStats(child, currentDepth + 1);
                stats.totalNodes += childStats.totalNodes;
                stats.leafNodes += childStats.leafNodes;
                stats.mappedNodes += childStats.mappedNodes;
                stats.maxDepth = Math.max(stats.maxDepth, childStats.maxDepth);
            }
        }
        
        return stats;
    }
    
    /**
     * 内部类：树统计信息
     */
    private static class TreeStats {
        int totalNodes = 0;
        int leafNodes = 0;
        int mappedNodes = 0;
        int maxDepth = 0;
    }
    
    /**
     * 查找指定标题的节点
     * @param treeNodes 目录树根节点列表
     * @param title 要查找的标题
     * @return 找到的节点，如果没找到返回null
     */
    public static TocTreeNode findNodeByTitle(List<TocTreeNode> treeNodes, String title) {
        for (TocTreeNode rootNode : treeNodes) {
            TocTreeNode found = findNodeByTitleRecursive(rootNode, title);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * 递归查找节点
     */
    private static TocTreeNode findNodeByTitleRecursive(TocTreeNode node, String title) {
        if (title.equals(node.getTitle())) {
            return node;
        }
        
        if (node.getChildren() != null) {
            for (TocTreeNode child : node.getChildren()) {
                TocTreeNode found = findNodeByTitleRecursive(child, title);
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 验证树结构的完整性
     * @param treeNodes 目录树根节点列表
     * @return 验证结果
     */
    public static boolean validateTreeStructure(List<TocTreeNode> treeNodes) {
        try {
            for (TocTreeNode rootNode : treeNodes) {
                if (!validateNodeStructure(rootNode, null)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("树结构验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 递归验证节点结构
     */
    private static boolean validateNodeStructure(TocTreeNode node, TocTreeNode expectedParent) {
        // 检查父节点引用
        if (node.getParent() != expectedParent) {
            log.error("节点 {} 的父节点引用不正确", node.getTitle());
            return false;
        }
        
        // 检查子节点
        if (node.getChildren() != null) {
            for (TocTreeNode child : node.getChildren()) {
                if (!validateNodeStructure(child, node)) {
                    return false;
                }
            }
        }
        
        return true;
    }
}
