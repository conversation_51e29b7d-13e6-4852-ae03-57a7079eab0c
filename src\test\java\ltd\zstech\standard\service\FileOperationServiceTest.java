package ltd.zstech.standard.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 文件操作服务测试类
 */
@SpringBootTest
@Slf4j
public class FileOperationServiceTest {

    @Resource
    private FileOperationService fileOperationService;

    /**
     * 测试目录项识别功能
     */
    @Test
    public void testTocPatternRecognition() {
        log.info("开始测试目录项识别功能");
        fileOperationService.testTocPatternRecognition();
        log.info("目录项识别功能测试完成");
    }

    /**
     * 测试文件名清理功能
     */
    @Test
    public void testFileNameSanitization() {
        log.info("开始测试文件名清理功能");
        fileOperationService.testFileNameSanitization();
        log.info("文件名清理功能测试完成");
    }

    /**
     * 测试扩展目录列表功能
     */
    @Test
    public void testExtendedTocList() {
        log.info("开始测试扩展目录列表功能");
        fileOperationService.testExtendedTocList();
        log.info("扩展目录列表功能测试完成");
    }

    /**
     * 测试原始目录重新索引功能
     */
    @Test
    public void testOriginalTocReindexing() {
        log.info("开始测试原始目录重新索引功能");
        fileOperationService.testOriginalTocReindexing();
        log.info("原始目录重新索引功能测试完成");
    }

    /**
     * 测试映射结果中的原始目录标题功能
     */
    @Test
    public void testMappingWithOriginalTocTitle() {
        log.info("开始测试映射结果中的原始目录标题功能");
        fileOperationService.testMappingWithOriginalTocTitle();
        log.info("映射结果中的原始目录标题功能测试完成");
    }

    /**
     * 测试智能映射逻辑（跳过首页和目次）
     */
    @Test
    public void testSmartMappingLogic() {
        log.info("开始测试智能映射逻辑");
        fileOperationService.testSmartMappingLogic();
        log.info("智能映射逻辑测试完成");
    }

    /**
     * 测试层级信息处理
     */
    @Test
    public void testHierarchicalInfo() {
        log.info("开始测试层级信息处理");
        fileOperationService.testHierarchicalInfo();
        log.info("层级信息处理测试完成");
    }

    /**
     * 测试文档拆分和目录映射功能
     */
    @Test
    public void testSplitDocumentAndMapWithTOC() {
        try {
            // 创建模拟的Word文档文件
            String testContent = "测试文档内容";
            InputStream stream = new ByteArrayInputStream(testContent.getBytes());
            MultipartFile file = new MockMultipartFile("test.docx", "test.docx", 
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", stream);

            // 执行文档拆分和目录映射
            JSONObject result = fileOperationService.splitDocumentAndMapWithTOC(file, 1, 1);

            // 验证结果
            assert result != null;
            assert result.containsKey("splitDocuments");
            assert result.containsKey("tocFromPage");
            assert result.containsKey("tocFromHeadings");
            assert result.containsKey("mappingResults");

            log.info("文档拆分和目录映射测试结果: {}", result.toJSONString());

        } catch (Exception e) {
            log.error("测试文档拆分和目录映射功能失败", e);
        }
    }

    /**
     * 测试目录提取功能
     */
    @Test
    public void testGetDocumentTableOfContents() {
        try {
            // 创建模拟的Word文档文件
            String testContent = "测试文档内容";
            InputStream stream = new ByteArrayInputStream(testContent.getBytes());
            MultipartFile file = new MockMultipartFile("test.docx", "test.docx", 
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", stream);

            // 执行目录提取
            JSONObject result = fileOperationService.getDocumentTableOfContents(file);

            // 验证结果
            assert result != null;
            assert result.containsKey("flatToc");
            assert result.containsKey("hierarchicalToc");
            assert result.containsKey("totalItems");

            log.info("目录提取测试结果: {}", result.toJSONString());

        } catch (Exception e) {
            log.error("测试目录提取功能失败", e);
        }
    }

    /**
     * 测试标题匹配算法
     */
    @Test
    public void testTitleMatching() {
        // 测试数据
        String[][] testCases = {
            {"设备缺陷管理制度", "设备缺陷管理制度", "1.0"}, // 完全匹配
            {"设备缺陷管理制度", "设备缺陷管理", "0.8"}, // 包含匹配
            {"前言", "前  言", "1.0"}, // 空格差异
            {"1 范围", "1范围", "1.0"}, // 空格差异
            {"6.1 设备分级", "6.1设备分级", "1.0"}, // 空格差异
            {"完全不同的标题", "另一个标题", "0.0"} // 不匹配
        };

        for (String[] testCase : testCases) {
            String title1 = testCase[0];
            String title2 = testCase[1];
            String expectedScore = testCase[2];

            // 这里需要通过反射或者将方法设为public来测试私有方法
            // 或者创建一个专门的测试方法
            log.info("测试标题匹配: [{}] vs [{}], 期望得分: {}", title1, title2, expectedScore);
        }
    }

    /**
     * 测试文件名标题提取
     */
    @Test
    public void testExtractTitleFromFileName() {
        String[] testFileNames = {
            "设备缺陷管理制度 - 0.docx",
            "前言 - 1.docx",
            "1 范围 - 2.docx",
            "6.1 设备分级 - 3.docx",
            "附录 - 4.docx"
        };

        for (String fileName : testFileNames) {
            // 这里需要通过反射或者将方法设为public来测试私有方法
            log.info("测试文件名: [{}]", fileName);
        }
    }
}
